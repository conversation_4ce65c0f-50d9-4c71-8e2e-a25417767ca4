package com.avinyaops.procurement.organization.department;

import com.avinyaops.procurement.organization.Organization;
import com.avinyaops.procurement.organization.OrganizationService;
import com.avinyaops.procurement.organization.ResourceInUseException;
import com.avinyaops.procurement.user.User;
import com.avinyaops.procurement.user.UserRepository;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class DepartmentServiceImpl implements DepartmentService {
    private final DepartmentRepository departmentRepository;
    private final OrganizationService organizationService;
    private final UserRepository userRepository;

    @Override
    public DepartmentDTO createDepartment(DepartmentDTO departmentDTO) {
        // Check if department with same name exists in the organization
        if (departmentRepository.existsByNameAndOrganizationId(departmentDTO.getName(), departmentDTO.getOrganizationId())) {
            throw new IllegalArgumentException("Department with name '" + departmentDTO.getName() + "' already exists in the organization");
        }

        Department department = convertToEntity(departmentDTO);
        department = departmentRepository.save(department);
        return convertToDTO(department);
    }

    //TODO: create custom exception for departments as well.
    @Override
    public DepartmentDTO updateDepartment(Long id, DepartmentDTO departmentDTO) {
        Department existingDepartment = departmentRepository.findByIdAndOrganizationId(id, departmentDTO.getOrganizationId())
                .orElseThrow(() -> new EntityNotFoundException("Department not found with id: " + id));

        // Check if name is being changed and if new name exists in the organization
        if (!existingDepartment.getName().equals(departmentDTO.getName()) &&
            departmentRepository.existsByNameAndOrganizationId(departmentDTO.getName(), departmentDTO.getOrganizationId())) {
            throw new IllegalArgumentException("Department with name '" + departmentDTO.getName() + "' already exists in the organization");
        }

        existingDepartment.setName(departmentDTO.getName());
        existingDepartment.setDescription(departmentDTO.getDescription());
        existingDepartment.setDepartmentHead(departmentDTO.getDepartmentHeadId() != null ? userRepository.findById(departmentDTO.getDepartmentHeadId()).orElse(null) : null);

        existingDepartment = departmentRepository.save(existingDepartment);
        return convertToDTO(existingDepartment);
    }

    @Override
    public void deleteDepartment(Long id, Long organizationId) {
        Department department = departmentRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new EntityNotFoundException("Department not found with id: " + id));

        // Check if department has any active employees
        if (userRepository.existsByDepartmentId(id)) {
            throw new ResourceInUseException("Cannot delete department as it has active employees associated with it");
        }

        department.softDelete();
        departmentRepository.save(department);
    }

    @Override
    @Transactional(readOnly = true)
    public DepartmentDTO getDepartment(Long id, Long organizationId) {
        Department department = departmentRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new EntityNotFoundException("Department not found with id: " + id));
        return convertToDTO(department);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DepartmentDTO> getAllDepartmentsByOrganization(Long organizationId) {
        return departmentRepository.findAllByOrganizationId(organizationId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    private DepartmentDTO convertToDTO(Department department) {
        return DepartmentDTO.builder()
                .id(department.getId())
                .name(department.getName())
                .description(department.getDescription())
                .organizationId(department.getOrganization().getId())
                .departmentHeadId(Optional.ofNullable(department.getDepartmentHead()).map(User::getId).orElse(null))
                .build();
    }

    private Department convertToEntity(DepartmentDTO dto) {
        Organization organization = organizationService.getOrganizationEntity(dto.getOrganizationId());
        return Department.builder()
                .name(dto.getName())
                .description(dto.getDescription())
                .organization(organization)
                .departmentHead(dto.getDepartmentHeadId() != null ? userRepository.findById(dto.getDepartmentHeadId()).orElse(null) : null)
                .build();
    }
} 